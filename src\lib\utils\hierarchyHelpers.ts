import { Project, ProjectHierarchy, TableRowData, HierarchicalTableData } from "@/lib/types/projects";
import { Outcome, isOutcomeCompleted } from "@/lib/types/outcomes";
import { ResolvedCustomField } from "@/lib/types/customFields";

/**
 * Normalize project data to use consistent field names
 */
function normalizeProject(project: Project): Project {
  return {
    ...project,
    children: project.children || project.sub_projects || [],
  };
}

/**
 * Calculate the total number of leaf nodes (projects without children) in a project tree
 */
function countLeafNodes(project: Project): number {
  const normalizedProject = normalizeProject(project);
  if (!normalizedProject.children || normalizedProject.children.length === 0) {
    return 1;
  }

  return normalizedProject.children.reduce((total, child) => total + countLeafNodes(child), 0);
}

/**
 * Calculate the maximum depth of a project hierarchy
 */
function calculateMaxDepth(projects: Project[]): number {
  if (!projects || projects.length === 0) return 0;

  let maxDepth = 1;

  for (const project of projects) {
    const normalizedProject = normalizeProject(project);
    if (normalizedProject.children && normalizedProject.children.length > 0) {
      const childDepth = 1 + calculateMaxDepth(normalizedProject.children);
      maxDepth = Math.max(maxDepth, childDepth);
    }
  }

  return maxDepth;
}

/**
 * Calculate completion metrics for a project including all its descendants
 */
function calculateProjectCompletionMetrics(
  project: Project,
  allOutcomes: Outcome[] = []
): { totalOutcomes: number; completedOutcomes: number; completionPercentage: number } {
  // Get all project IDs in this project's tree (including itself)
  const projectIds = getAllProjectIds(project);

  // Filter outcomes for this project tree
  const projectOutcomes = allOutcomes.filter((outcome) => projectIds.includes(outcome.project));

  const totalOutcomes = projectOutcomes.length;
  const completedOutcomes = projectOutcomes.filter(isOutcomeCompleted).length;
  const completionPercentage = totalOutcomes > 0 ? Math.round((completedOutcomes / totalOutcomes) * 100) : 0;

  return {
    totalOutcomes,
    completedOutcomes,
    completionPercentage,
  };
}

/**
 * Get all project IDs in a project tree (including the project itself and all descendants)
 */
function getAllProjectIds(project: Project): string[] {
  const ids = [project.id];
  const normalizedProject = normalizeProject(project);

  if (normalizedProject.children && normalizedProject.children.length > 0) {
    for (const child of normalizedProject.children) {
      ids.push(...getAllProjectIds(child));
    }
  }

  return ids;
}

/**
 * Create table rows for a Life Aspect, ensuring all sibling projects are included
 */
function createLifeAspectRows(
  projects: Project[],
  lifeAspectName: string,
  lifeAspectId: string,
  maxDepth: number,
  allOutcomes: Outcome[] = []
): TableRowData[] {
  if (!projects || projects.length === 0) {
    return [];
  }

  // Collect all complete project paths from root to leaf
  const allProjectPaths: Project[][] = [];

  // Recursive function to collect all paths - this ensures ALL sibling projects are processed
  function collectAllPaths(projectList: Project[], currentPath: Project[] = []) {
    // Process each project at this level - CRITICAL: using for...of to ensure all siblings are processed
    for (const project of projectList) {
      const normalizedProject = normalizeProject(project);
      const newPath = [...currentPath, normalizedProject];

      if (normalizedProject.children && normalizedProject.children.length > 0) {
        // This project has children, continue traversing to collect all descendant paths
        collectAllPaths(normalizedProject.children, newPath);
      } else {
        // This is a leaf project, add the complete path from root to leaf
        allProjectPaths.push(newPath);
      }
    }
  }

  collectAllPaths(projects);

  // If no leaf paths found (all projects are parents without children), create paths for root projects
  if (allProjectPaths.length === 0) {
    for (const project of projects) {
      allProjectPaths.push([normalizeProject(project)]);
    }
  }

  // Create table rows from all collected paths - each path becomes one row
  const rows: TableRowData[] = [];

  for (let pathIndex = 0; pathIndex < allProjectPaths.length; pathIndex++) {
    const path = allProjectPaths[pathIndex];

    // Initialize project levels array for this row
    const projectLevels = [];
    for (let i = 0; i < maxDepth; i++) {
      if (i < path.length) {
        const project = path[i];

        // TODO: Implement outcome calculation logic if needed
        const outcomeStats = {
          totalOutcomes: 0,
          completedOutcomes: 0,
          completionPercentage: 0,
        };

        projectLevels[i] = {
          name: project.name,
          projectId: project.id,
          level: i,
          rowSpan: 1,
          shouldRender: true,
          priorityLevel: project.priority_level,
          priorityLevelName: project.priority_level_name,
          priorityLevelColor: project.priority_level_color,
          customFields: project.resolved_custom_fields || [],
          startDate: project.start_date,
          endDate: project.end_date,
          totalOutcomes: outcomeStats.totalOutcomes,
          completedOutcomes: outcomeStats.completedOutcomes,
          completionPercentage: outcomeStats.completionPercentage,
          parentProjectId: project.parent_project,
        };
      } else {
        projectLevels[i] = {
          name: "",
          projectId: "",
          level: i,
          rowSpan: 1,
          shouldRender: false,
          totalOutcomes: 0,
          completedOutcomes: 0,
          completionPercentage: 0,
        };
      }
    }

    // Create row data - use the leaf project as the primary project
    const leafProject = path[path.length - 1];
    const rowData: TableRowData = {
      id: leafProject.id,
      lifeAspectName,
      lifeAspectId,
      lifeAspectRowSpan: pathIndex === 0 ? allProjectPaths.length : 0, // Only set rowspan for first row
      projectLevels,
      maxDepth,
      name: leafProject.name,
      description: leafProject.description || "",
      start_date: leafProject.start_date || null,
      end_date: leafProject.end_date || null,
      resolved_custom_fields: leafProject.resolved_custom_fields || [],
      hasChildren: normalizeProject(leafProject).children && normalizeProject(leafProject).children!.length > 0,
      isExpanded: true,
      rowSpan: 1,
      parent_id: leafProject.parent_project || null,
    };

    rows.push(rowData);
  }

  return rows;
}

/**
 * Transform project hierarchy data into table-ready format with rowSpan calculations
 */
export function transformHierarchyToTableData(hierarchyData: ProjectHierarchy[], allOutcomes: Outcome[] = []): HierarchicalTableData {
  // Validate input
  if (!hierarchyData || !Array.isArray(hierarchyData) || hierarchyData.length === 0) {
    console.log("No valid hierarchy data provided, returning empty structure");
    return {
      rows: [],
      maxDepth: 0,
      columnHeaders: ["Life Aspect"],
    };
  }

  // Calculate overall maximum depth across all life aspects
  const depths = hierarchyData
    .filter((item) => item && item.projects) // Filter out invalid items
    .map((item) => calculateMaxDepth((item.projects || []).map(normalizeProject)));

  const globalMaxDepth = depths.length > 0 ? Math.max(...depths) : 0;

  // Generate column headers
  const columnHeaders = ["Life Aspect"];
  for (let i = 0; i < globalMaxDepth; i++) {
    columnHeaders.push(`Level ${i}`);
  }

  const allRows: TableRowData[] = [];

  // Process each life aspect
  for (const lifeAspectData of hierarchyData) {
    const { life_aspect, projects } = lifeAspectData;

    // Validate life_aspect data
    if (!life_aspect) {
      console.warn("Invalid life aspect data found:", lifeAspectData);
      continue;
    }

    // Ensure life_aspect has required fields
    const lifeAspectId = life_aspect.id || `temp-${Date.now()}-${Math.random()}`;
    const lifeAspectName = life_aspect.name || "Unknown Life Aspect";

    if (!projects || projects.length === 0) {
      // Life aspect with no projects - create empty row
      const emptyProjectLevels = [];
      for (let i = 0; i < globalMaxDepth; i++) {
        emptyProjectLevels[i] = {
          name: "",
          projectId: "",
          level: i,
          rowSpan: 0,
          shouldRender: false,
          totalOutcomes: 0,
          completedOutcomes: 0,
          completionPercentage: 0,
        };
      }

      allRows.push({
        id: `empty-${lifeAspectId}`,
        lifeAspectName: lifeAspectName,
        lifeAspectId: lifeAspectId,
        lifeAspectRowSpan: 1,
        projectLevels: emptyProjectLevels,
        maxDepth: globalMaxDepth,
        name: "",
        description: "",
        start_date: null,
        end_date: null,
        resolved_custom_fields: [],
        hasChildren: false,
        isExpanded: true,
        rowSpan: 1,
        parent_id: null,
      });
      continue;
    }

    // Normalize projects to ensure consistent structure
    const normalizedProjects = projects.map(normalizeProject);

    // Create rows for this life aspect - ensures all sibling projects are included
    const lifeAspectRows = createLifeAspectRows(normalizedProjects, lifeAspectName, lifeAspectId, globalMaxDepth, allOutcomes);

    allRows.push(...lifeAspectRows);
  }

  return {
    rows: allRows,
    maxDepth: globalMaxDepth,
    columnHeaders,
  };
}

/**
 * Helper function to get the display value for a table cell
 */
export function getCellDisplayValue(row: TableRowData, columnIndex: number): { value: string; rowSpan: number; shouldRender: boolean } {
  if (columnIndex === 0) {
    // Life Aspect column
    return {
      value: row.lifeAspectName,
      rowSpan: row.lifeAspectRowSpan,
      shouldRender: row.lifeAspectRowSpan > 0,
    };
  }

  // Project level columns (columnIndex - 1 gives us the project level)
  const projectLevelIndex = columnIndex - 1;

  if (projectLevelIndex < row.projectLevels.length) {
    const projectLevel = row.projectLevels[projectLevelIndex];
    return {
      value: projectLevel.name || "",
      rowSpan: projectLevel.rowSpan || 1,
      shouldRender: projectLevel.shouldRender || false,
    };
  }

  // Empty cell
  return {
    value: "",
    rowSpan: 1,
    shouldRender: false,
  };
}

/**
 * Helper function to get project data for a table cell
 */
export function getCellProjectData(row: TableRowData, columnIndex: number): any | null {
  // Project level columns (columnIndex - 1 gives us the project level)
  const projectLevelIndex = columnIndex - 1;

  if (projectLevelIndex >= 0 && projectLevelIndex < row.projectLevels.length) {
    return row.projectLevels[projectLevelIndex];
  }

  return null;
}
